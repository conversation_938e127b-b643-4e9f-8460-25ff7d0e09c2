import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

import { useTheme } from '../components/ThemeProvider';
import { 
  loadHistory, 
  loadSettings 
} from '../utils/storage';
import { 
  calculateWeeklyTotals, 
  calculateMonthlyTotals, 
  formatCurrency, 
  formatUnits 
} from '../utils/calculations';
import { HistoryEntry, AppSettings, PurchaseData, UsageData } from '../types';

type FilterType = 'all' | 'weekly' | 'monthly';
type EntryType = 'all' | 'purchases' | 'usage';

const History: React.FC = () => {
  const { theme } = useTheme();
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [filteredHistory, setFilteredHistory] = useState<HistoryEntry[]>([]);
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [entryType, setEntryType] = useState<EntryType>('all');
  const [weeklyTotals, setWeeklyTotals] = useState({ usage: 0, cost: 0, purchases: 0 });
  const [monthlyTotals, setMonthlyTotals] = useState({ usage: 0, cost: 0, purchases: 0 });

  useFocusEffect(
    useCallback(() => {
      loadHistoryData();
    }, [])
  );

  useEffect(() => {
    applyFilters();
  }, [history, filterType, entryType]);

  const loadHistoryData = async () => {
    try {
      const [historyData, appSettings] = await Promise.all([
        loadHistory(),
        loadSettings(),
      ]);

      setHistory(historyData);
      setSettings(appSettings);

      // Calculate totals
      const weekly = calculateWeeklyTotals(historyData);
      const monthly = calculateMonthlyTotals(historyData);
      setWeeklyTotals(weekly);
      setMonthlyTotals(monthly);
    } catch (error) {
      console.error('Error loading history:', error);
      Alert.alert('Error', 'Failed to load history data.');
    }
  };

  const applyFilters = () => {
    let filtered = [...history];

    // Apply time filter
    if (filterType === 'weekly') {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      filtered = filtered.filter(entry => new Date(entry.timestamp) >= oneWeekAgo);
    } else if (filterType === 'monthly') {
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      filtered = filtered.filter(entry => new Date(entry.timestamp) >= oneMonthAgo);
    }

    // Apply entry type filter
    if (entryType !== 'all') {
      filtered = filtered.filter(entry => entry.type === entryType);
    }

    setFilteredHistory(filtered);
  };

  const renderFilterButton = (label: string, value: FilterType | EntryType, currentValue: FilterType | EntryType, onPress: (value: any) => void) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        { 
          backgroundColor: value === currentValue ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.primary,
        }
      ]}
      onPress={() => onPress(value)}>
      <Text style={[
        styles.filterButtonText,
        { color: value === currentValue ? '#FFFFFF' : theme.colors.text }
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderHistoryItem = ({ item }: { item: HistoryEntry }) => {
    const isPurchase = item.type === 'purchase';
    const data = item.data as PurchaseData | UsageData;
    const timestamp = new Date(item.timestamp);
    
    const currencySymbol = settings?.currency === 'CUSTOM' ? settings.customCurrency : 
      settings?.currency === 'USD' ? '$' : 
      settings?.currency === 'EUR' ? '€' : 
      settings?.currency === 'GBP' ? '£' : 
      settings?.currency === 'ZAR' ? 'R' : 
      settings?.currency === 'JPY' ? '¥' : settings?.currency || '';

    const unitType = settings?.unitType === 'CUSTOM' ? settings.customUnitType : settings?.unitType || 'Units';

    return (
      <View style={[styles.historyItem, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.historyItemHeader}>
          <View style={styles.historyItemIcon}>
            <Icon 
              name={isPurchase ? 'add-shopping-cart' : 'trending-up'} 
              size={24} 
              color={isPurchase ? theme.colors.success : theme.colors.secondary} 
            />
          </View>
          <View style={styles.historyItemInfo}>
            <Text style={[styles.historyItemTitle, { color: theme.colors.text }]}>
              {isPurchase ? 'Purchase' : 'Usage Recording'}
            </Text>
            <Text style={[styles.historyItemDate, { color: theme.colors.textSecondary }]}>
              {timestamp.toLocaleDateString()} at {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </View>
        </View>
        
        <View style={styles.historyItemDetails}>
          {isPurchase ? (
            <>
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Amount Spent:</Text>
                <Text style={[styles.detailValue, { color: theme.colors.text }]}>
                  {formatCurrency((data as PurchaseData).currencyAmount, (data as PurchaseData).currency, currencySymbol)}
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Units Received:</Text>
                <Text style={[styles.detailValue, { color: theme.colors.success }]}>
                  {formatUnits((data as PurchaseData).unitsReceived, unitType)}
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Cost per Unit:</Text>
                <Text style={[styles.detailValue, { color: theme.colors.text }]}>
                  {formatCurrency((data as PurchaseData).costPerUnit, (data as PurchaseData).currency, currencySymbol)}
                </Text>
              </View>
            </>
          ) : (
            <>
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Previous Reading:</Text>
                <Text style={[styles.detailValue, { color: theme.colors.text }]}>
                  {formatUnits((data as UsageData).previousReading, unitType)}
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Current Reading:</Text>
                <Text style={[styles.detailValue, { color: theme.colors.text }]}>
                  {formatUnits((data as UsageData).currentReading, unitType)}
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Units Used:</Text>
                <Text style={[styles.detailValue, { color: theme.colors.secondary }]}>
                  {formatUnits((data as UsageData).unitsUsed, unitType)}
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Cost:</Text>
                <Text style={[styles.detailValue, { color: theme.colors.text }]}>
                  {formatCurrency((data as UsageData).cost, settings?.currency || 'USD', currencySymbol)}
                </Text>
              </View>
            </>
          )}
        </View>
      </View>
    );
  };

  if (!settings) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Icon name="history" size={48} color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading History...</Text>
        </View>
      </View>
    );
  }

  const currencySymbol = settings.currency === 'CUSTOM' ? settings.customCurrency : 
    settings.currency === 'USD' ? '$' : 
    settings.currency === 'EUR' ? '€' : 
    settings.currency === 'GBP' ? '£' : 
    settings.currency === 'ZAR' ? 'R' : 
    settings.currency === 'JPY' ? '¥' : settings.currency;

  const unitType = settings.unitType === 'CUSTOM' ? settings.customUnitType : settings.unitType;

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.headerCard}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}>
        <Icon name="history" size={32} color="#FFFFFF" />
        <Text style={styles.headerTitle}>History</Text>
        <Text style={styles.headerSubtitle}>
          View your purchase and usage records
        </Text>
      </LinearGradient>

      {/* Totals Cards */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.totalsContainer}>
        <View style={[styles.totalCard, { backgroundColor: theme.colors.surface }]}>
          <Icon name="date-range" size={24} color={theme.colors.primary} />
          <Text style={[styles.totalLabel, { color: theme.colors.textSecondary }]}>Weekly</Text>
          <Text style={[styles.totalValue, { color: theme.colors.text }]}>
            {formatUnits(weeklyTotals.usage, unitType)}
          </Text>
          <Text style={[styles.totalSubvalue, { color: theme.colors.textSecondary }]}>
            {formatCurrency(weeklyTotals.cost, settings.currency, currencySymbol)}
          </Text>
        </View>

        <View style={[styles.totalCard, { backgroundColor: theme.colors.surface }]}>
          <Icon name="calendar-today" size={24} color={theme.colors.secondary} />
          <Text style={[styles.totalLabel, { color: theme.colors.textSecondary }]}>Monthly</Text>
          <Text style={[styles.totalValue, { color: theme.colors.text }]}>
            {formatUnits(monthlyTotals.usage, unitType)}
          </Text>
          <Text style={[styles.totalSubvalue, { color: theme.colors.textSecondary }]}>
            {formatCurrency(monthlyTotals.cost, settings.currency, currencySymbol)}
          </Text>
        </View>

        <View style={[styles.totalCard, { backgroundColor: theme.colors.surface }]}>
          <Icon name="shopping-cart" size={24} color={theme.colors.success} />
          <Text style={[styles.totalLabel, { color: theme.colors.textSecondary }]}>Weekly Purchases</Text>
          <Text style={[styles.totalValue, { color: theme.colors.text }]}>
            {formatCurrency(weeklyTotals.purchases, settings.currency, currencySymbol)}
          </Text>
        </View>

        <View style={[styles.totalCard, { backgroundColor: theme.colors.surface }]}>
          <Icon name="account-balance-wallet" size={24} color={theme.colors.success} />
          <Text style={[styles.totalLabel, { color: theme.colors.textSecondary }]}>Monthly Purchases</Text>
          <Text style={[styles.totalValue, { color: theme.colors.text }]}>
            {formatCurrency(monthlyTotals.purchases, settings.currency, currencySymbol)}
          </Text>
        </View>
      </ScrollView>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <Text style={[styles.filterTitle, { color: theme.colors.text }]}>Time Period:</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          {renderFilterButton('All Time', 'all', filterType, setFilterType)}
          {renderFilterButton('This Week', 'weekly', filterType, setFilterType)}
          {renderFilterButton('This Month', 'monthly', filterType, setFilterType)}
        </ScrollView>

        <Text style={[styles.filterTitle, { color: theme.colors.text }]}>Entry Type:</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          {renderFilterButton('All', 'all', entryType, setEntryType)}
          {renderFilterButton('Purchases', 'purchases', entryType, setEntryType)}
          {renderFilterButton('Usage', 'usage', entryType, setEntryType)}
        </ScrollView>
      </View>

      {/* History List */}
      <FlatList
        data={filteredHistory}
        renderItem={renderHistoryItem}
        keyExtractor={(item) => item.id}
        style={styles.historyList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="history" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              No history entries found
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
              Start by making a purchase or recording usage
            </Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    marginTop: 16,
    fontWeight: '500',
  },
  headerCard: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  headerSubtitle: {
    color: '#FFFFFF',
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.9,
  },
  totalsContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  totalCard: {
    padding: 16,
    borderRadius: 12,
    marginRight: 12,
    minWidth: 140,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  totalLabel: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
    textAlign: 'center',
  },
  totalSubvalue: {
    fontSize: 12,
    marginTop: 2,
    textAlign: 'center',
  },
  filtersContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  filterRow: {
    marginBottom: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  historyList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  historyItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  historyItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  historyItemIcon: {
    marginRight: 12,
  },
  historyItemInfo: {
    flex: 1,
  },
  historyItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  historyItemDate: {
    fontSize: 12,
    marginTop: 2,
  },
  historyItemDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default History;

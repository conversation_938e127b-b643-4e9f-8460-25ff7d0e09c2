import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { LineChart } from 'react-native-chart-kit';

import { useTheme } from '../components/ThemeProvider';
import { 
  loadSettings, 
  saveUsage, 
  loadElectricityData, 
  saveElectricityData,
  loadHistory 
} from '../utils/storage';
import { 
  calculateUsage, 
  calculateCost, 
  formatCurrency, 
  formatUnits,
  generateChartData 
} from '../utils/calculations';
import { AppSettings, UsageData, ElectricityData } from '../types';

const { width } = Dimensions.get('window');

const Usage: React.FC = () => {
  const { theme } = useTheme();
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [electricityData, setElectricityData] = useState<ElectricityData | null>(null);
  const [currentReading, setCurrentReading] = useState('');
  const [usageCalculated, setUsageCalculated] = useState(0);
  const [costCalculated, setCostCalculated] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [chartData, setChartData] = useState<any>(null);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (currentReading && electricityData && settings) {
      const reading = parseFloat(currentReading) || 0;
      const usage = calculateUsage(electricityData.currentUnits, reading);
      const cost = calculateCost(usage, settings.costPerUnit);
      setUsageCalculated(usage);
      setCostCalculated(cost);
    } else {
      setUsageCalculated(0);
      setCostCalculated(0);
    }
  }, [currentReading, electricityData, settings]);

  const loadData = async () => {
    try {
      const [appSettings, data, history] = await Promise.all([
        loadSettings(),
        loadElectricityData(),
        loadHistory(),
      ]);
      
      setSettings(appSettings);
      setElectricityData(data);

      // Generate chart data
      if (history.length > 0) {
        const chart = generateChartData(history, 7);
        setChartData(chart);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load data.');
    }
  };

  const handleRecordUsage = async () => {
    if (!currentReading || !electricityData || !settings) {
      Alert.alert('Error', 'Please enter a valid current reading.');
      return;
    }

    const reading = parseFloat(currentReading);
    if (reading < 0) {
      Alert.alert('Error', 'Please enter a valid reading greater than or equal to 0.');
      return;
    }

    setIsLoading(true);

    try {
      // Create usage record
      const usage: UsageData = {
        id: Date.now().toString(),
        previousReading: electricityData.currentUnits,
        currentReading: reading,
        unitsUsed: usageCalculated,
        timestamp: new Date(),
        cost: costCalculated,
      };

      // Save usage to history
      await saveUsage(usage);

      // Update electricity data
      const updatedData: ElectricityData = {
        ...electricityData,
        previousUnits: electricityData.currentUnits,
        currentUnits: reading,
        usageSinceLastRecording: usageCalculated,
        timestamp: new Date(),
      };
      await saveElectricityData(updatedData);

      Alert.alert(
        'Usage Recorded',
        `Successfully recorded ${formatUnits(usageCalculated, settings.unitType === 'CUSTOM' ? settings.customUnitType || 'Units' : settings.unitType)} usage.`,
        [
          {
            text: 'OK',
            onPress: () => {
              setCurrentReading('');
              setUsageCalculated(0);
              setCostCalculated(0);
              loadData(); // Reload data to update chart
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error recording usage:', error);
      Alert.alert('Error', 'Failed to record usage. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!settings || !electricityData) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Icon name="trending-up" size={48} color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading...</Text>
        </View>
      </View>
    );
  }

  const currencySymbol = settings.currency === 'CUSTOM' ? settings.customCurrency : 
    settings.currency === 'USD' ? '$' : 
    settings.currency === 'EUR' ? '€' : 
    settings.currency === 'GBP' ? '£' : 
    settings.currency === 'ZAR' ? 'R' : 
    settings.currency === 'JPY' ? '¥' : settings.currency;

  const unitType = settings.unitType === 'CUSTOM' ? settings.customUnitType : settings.unitType;

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        
        {/* Header */}
        <LinearGradient
          colors={theme.gradients.primary}
          style={styles.headerCard}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}>
          <Icon name="trending-up" size={32} color="#FFFFFF" />
          <Text style={styles.headerTitle}>Record Usage</Text>
          <Text style={styles.headerSubtitle}>
            Enter your current meter reading to calculate usage
          </Text>
        </LinearGradient>

        {/* Current Reading Display */}
        <View style={[styles.currentReadingCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.readingRow}>
            <Text style={[styles.readingLabel, { color: theme.colors.textSecondary }]}>
              Previous Reading:
            </Text>
            <Text style={[styles.readingValue, { color: theme.colors.text }]}>
              {formatUnits(electricityData.currentUnits, unitType)}
            </Text>
          </View>
        </View>

        {/* Current Reading Input */}
        <View style={[styles.inputCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.inputHeader}>
            <Icon name="speed" size={24} color={theme.colors.primary} />
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Current Reading ({unitType})
            </Text>
          </View>
          <TextInput
            style={[
              styles.readingInput,
              { 
                color: theme.colors.text,
                borderColor: theme.colors.primary,
                backgroundColor: theme.colors.background,
              }
            ]}
            value={currentReading}
            onChangeText={setCurrentReading}
            placeholder={`Enter current ${unitType} reading`}
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
            returnKeyType="done"
          />
        </View>

        {/* Usage Calculation */}
        <View style={[styles.calculationCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.calculationHeader}>
            <Icon name="calculate" size={24} color={theme.colors.secondary} />
            <Text style={[styles.calculationTitle, { color: theme.colors.text }]}>Usage Calculation</Text>
          </View>
          
          <View style={styles.calculationContent}>
            <View style={styles.calculationRow}>
              <Text style={[styles.calculationLabel, { color: theme.colors.textSecondary }]}>
                Units Used:
              </Text>
              <Text style={[styles.calculationValueLarge, { color: theme.colors.primary }]}>
                {formatUnits(usageCalculated, unitType)}
              </Text>
            </View>
            
            <View style={styles.calculationRow}>
              <Text style={[styles.calculationLabel, { color: theme.colors.textSecondary }]}>
                Cost per {unitType}:
              </Text>
              <Text style={[styles.calculationValue, { color: theme.colors.text }]}>
                {formatCurrency(settings.costPerUnit, settings.currency, currencySymbol)}
              </Text>
            </View>
            
            <View style={styles.calculationRow}>
              <Text style={[styles.calculationLabel, { color: theme.colors.textSecondary }]}>
                Total Cost:
              </Text>
              <Text style={[styles.calculationValue, { color: theme.colors.text }]}>
                {formatCurrency(costCalculated, settings.currency, currencySymbol)}
              </Text>
            </View>
          </View>
        </View>

        {/* Usage Chart */}
        {chartData && chartData.labels.length > 0 && (
          <View style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.chartHeader}>
              <Icon name="show-chart" size={24} color={theme.colors.secondary} />
              <Text style={[styles.chartTitle, { color: theme.colors.text }]}>7-Day Usage Chart</Text>
            </View>
            <LineChart
              data={chartData}
              width={width - 64}
              height={220}
              chartConfig={{
                backgroundColor: theme.colors.surface,
                backgroundGradientFrom: theme.colors.surface,
                backgroundGradientTo: theme.colors.surface,
                decimalPlaces: 1,
                color: (opacity = 1) => theme.colors.primary,
                labelColor: (opacity = 1) => theme.colors.textSecondary,
                style: {
                  borderRadius: 16,
                },
                propsForDots: {
                  r: '6',
                  strokeWidth: '2',
                  stroke: theme.colors.primary,
                },
              }}
              bezier
              style={styles.chart}
            />
          </View>
        )}

        {/* Record Button */}
        <TouchableOpacity
          style={[
            styles.recordButton,
            { 
              opacity: currentReading && !isLoading ? 1 : 0.5,
            }
          ]}
          onPress={handleRecordUsage}
          disabled={!currentReading || isLoading}>
          <LinearGradient
            colors={theme.gradients.primary}
            style={styles.recordButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}>
            <Icon 
              name={isLoading ? "hourglass-empty" : "save"} 
              size={24} 
              color="#FFFFFF" 
            />
            <Text style={styles.recordButtonText}>
              {isLoading ? 'Recording Usage...' : 'Record Usage'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        {/* Info Card */}
        <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
          <Icon name="info" size={20} color={theme.colors.secondary} />
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            Your usage will be calculated as the difference between your previous and current readings, and recorded in your history.
          </Text>
        </View>

      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    marginTop: 16,
    fontWeight: '500',
  },
  headerCard: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  headerSubtitle: {
    color: '#FFFFFF',
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.9,
  },
  currentReadingCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  readingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  readingLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  readingValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  inputCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  inputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  readingInput: {
    borderWidth: 2,
    borderRadius: 8,
    padding: 16,
    fontSize: 18,
    fontWeight: '500',
  },
  calculationCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  calculationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  calculationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  calculationContent: {
    gap: 12,
  },
  calculationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  calculationLabel: {
    fontSize: 14,
    flex: 1,
  },
  calculationValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  calculationValueLarge: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  chartCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  chartHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  recordButton: {
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  recordButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
  },
  recordButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  infoCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
});

export default Usage;

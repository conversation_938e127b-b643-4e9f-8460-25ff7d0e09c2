import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
  Modal,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

import { useTheme } from '../components/ThemeProvider';
import { 
  loadSettings, 
  saveSettings, 
  factoryReset, 
  dashboardDataReset,
  markInitialSetupComplete,
  saveElectricityData 
} from '../utils/storage';
import { 
  DEFAULT_CURRENCIES, 
  DEFAULT_UNIT_TYPES, 
  THEMES, 
  FONT_FAMILIES 
} from '../utils/constants';
import { AppSettings, ElectricityData } from '../types';

type SettingsSection = 'general' | 'appearance' | 'reset';

const Settings: React.FC = () => {
  const { theme, setTheme } = useTheme();
  const [settings, setSettingsState] = useState<AppSettings | null>(null);
  const [activeSection, setActiveSection] = useState<SettingsSection>('general');
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  const [showUnitModal, setShowUnitModal] = useState(false);
  const [showThemeModal, setShowThemeModal] = useState(false);
  const [customCurrency, setCustomCurrency] = useState('');
  const [customUnit, setCustomUnit] = useState('');
  const [initialUnits, setInitialUnits] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAppSettings();
  }, []);

  const loadAppSettings = async () => {
    try {
      const appSettings = await loadSettings();
      setSettingsState(appSettings);
      setCustomCurrency(appSettings.customCurrency || '');
      setCustomUnit(appSettings.customUnitType || '');
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const updateSettings = async (newSettings: Partial<AppSettings>) => {
    if (!settings) return;

    try {
      const updatedSettings = { ...settings, ...newSettings };
      await saveSettings(updatedSettings);
      setSettingsState(updatedSettings);
      
      // Update theme if theme was changed
      if (newSettings.theme) {
        setTheme(newSettings.theme);
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      Alert.alert('Error', 'Failed to update settings.');
    }
  };

  const handleInitialSetup = async () => {
    if (!initialUnits || !settings) {
      Alert.alert('Error', 'Please enter your current unit value.');
      return;
    }

    const units = parseFloat(initialUnits);
    if (units < 0) {
      Alert.alert('Error', 'Please enter a valid unit value.');
      return;
    }

    setIsLoading(true);

    try {
      const electricityData: ElectricityData = {
        id: Date.now().toString(),
        currentUnits: units,
        previousUnits: units,
        usageSinceLastRecording: 0,
        timestamp: new Date(),
      };

      await saveElectricityData(electricityData);
      await markInitialSetupComplete();

      Alert.alert(
        'Setup Complete',
        'Initial setup has been completed successfully!',
        [{ text: 'OK', onPress: () => setInitialUnits('') }]
      );
    } catch (error) {
      console.error('Error completing initial setup:', error);
      Alert.alert('Error', 'Failed to complete initial setup.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFactoryReset = () => {
    Alert.alert(
      'Factory Reset',
      'This will delete ALL data including settings, history, and current readings. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await factoryReset();
              Alert.alert('Reset Complete', 'All data has been cleared.');
              loadAppSettings();
            } catch (error) {
              Alert.alert('Error', 'Failed to perform factory reset.');
            }
          },
        },
      ]
    );
  };

  const handleDashboardReset = () => {
    Alert.alert(
      'Dashboard Data Reset',
      'This will clear your current unit readings but keep your history intact.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await dashboardDataReset();
              Alert.alert('Reset Complete', 'Dashboard data has been cleared.');
            } catch (error) {
              Alert.alert('Error', 'Failed to perform dashboard reset.');
            }
          },
        },
      ]
    );
  };

  const renderSectionButton = (title: string, section: SettingsSection, icon: string) => (
    <TouchableOpacity
      style={[
        styles.sectionButton,
        { 
          backgroundColor: activeSection === section ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.primary,
        }
      ]}
      onPress={() => setActiveSection(section)}>
      <Icon 
        name={icon} 
        size={20} 
        color={activeSection === section ? '#FFFFFF' : theme.colors.text} 
      />
      <Text style={[
        styles.sectionButtonText,
        { color: activeSection === section ? '#FFFFFF' : theme.colors.text }
      ]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderSettingItem = (
    title: string, 
    value: string, 
    onPress: () => void, 
    icon: string,
    subtitle?: string
  ) => (
    <TouchableOpacity 
      style={[styles.settingItem, { backgroundColor: theme.colors.surface }]} 
      onPress={onPress}>
      <View style={styles.settingItemLeft}>
        <Icon name={icon} size={24} color={theme.colors.primary} />
        <View style={styles.settingItemText}>
          <Text style={[styles.settingItemTitle, { color: theme.colors.text }]}>{title}</Text>
          {subtitle && (
            <Text style={[styles.settingItemSubtitle, { color: theme.colors.textSecondary }]}>
              {subtitle}
            </Text>
          )}
        </View>
      </View>
      <View style={styles.settingItemRight}>
        <Text style={[styles.settingItemValue, { color: theme.colors.textSecondary }]}>{value}</Text>
        <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
      </View>
    </TouchableOpacity>
  );

  if (!settings) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Icon name="settings" size={48} color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading Settings...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.headerCard}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}>
        <Icon name="settings" size={32} color="#FFFFFF" />
        <Text style={styles.headerTitle}>Settings</Text>
        <Text style={styles.headerSubtitle}>
          Configure your app preferences
        </Text>
      </LinearGradient>

      {/* Section Buttons */}
      <View style={styles.sectionButtonsContainer}>
        {renderSectionButton('General Settings', 'general', 'tune')}
        {renderSectionButton('Appearance', 'appearance', 'palette')}
        {renderSectionButton('Reset Options', 'reset', 'refresh')}
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* General Settings */}
        {activeSection === 'general' && (
          <View style={styles.sectionContainer}>
            {/* Initial Setup */}
            <View style={[styles.setupCard, { backgroundColor: theme.colors.surface }]}>
              <View style={styles.setupHeader}>
                <Icon name="flash-on" size={24} color={theme.colors.primary} />
                <Text style={[styles.setupTitle, { color: theme.colors.text }]}>Initial Setup</Text>
              </View>
              <Text style={[styles.setupDescription, { color: theme.colors.textSecondary }]}>
                Enter your current unit reading to start using the app
              </Text>
              <TextInput
                style={[
                  styles.setupInput,
                  { 
                    color: theme.colors.text,
                    borderColor: theme.colors.primary,
                    backgroundColor: theme.colors.background,
                  }
                ]}
                value={initialUnits}
                onChangeText={setInitialUnits}
                placeholder="Enter current units"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />
              <TouchableOpacity
                style={[styles.setupButton, { opacity: initialUnits && !isLoading ? 1 : 0.5 }]}
                onPress={handleInitialSetup}
                disabled={!initialUnits || isLoading}>
                <LinearGradient
                  colors={theme.gradients.primary}
                  style={styles.setupButtonGradient}>
                  <Text style={styles.setupButtonText}>
                    {isLoading ? 'Setting up...' : 'Complete Setup'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>

            {renderSettingItem(
              'Cost per Unit',
              `${settings.costPerUnit}`,
              () => {
                Alert.prompt(
                  'Cost per Unit',
                  'Enter the cost per unit:',
                  (text) => {
                    const cost = parseFloat(text);
                    if (!isNaN(cost) && cost > 0) {
                      updateSettings({ costPerUnit: cost });
                    }
                  },
                  'plain-text',
                  settings.costPerUnit.toString()
                );
              },
              'attach-money',
              'Set the cost per unit for calculations'
            )}

            {renderSettingItem(
              'Currency',
              settings.currency === 'CUSTOM' ? settings.customCurrency || 'Custom' : settings.currency,
              () => setShowCurrencyModal(true),
              'monetization-on',
              'Select your preferred currency'
            )}

            {renderSettingItem(
              'Unit Type',
              settings.unitType === 'CUSTOM' ? settings.customUnitType || 'Custom' : settings.unitType,
              () => setShowUnitModal(true),
              'speed',
              'Select your unit measurement type'
            )}

            {renderSettingItem(
              'Low Units Threshold',
              `${settings.lowUnitsThreshold}`,
              () => {
                Alert.prompt(
                  'Low Units Threshold',
                  'Enter the threshold for low units warning:',
                  (text) => {
                    const threshold = parseFloat(text);
                    if (!isNaN(threshold) && threshold >= 0) {
                      updateSettings({ lowUnitsThreshold: threshold });
                    }
                  },
                  'plain-text',
                  settings.lowUnitsThreshold.toString()
                );
              },
              'warning',
              'Get warned when units drop below this value'
            )}

            <View style={[styles.settingItem, { backgroundColor: theme.colors.surface }]}>
              <View style={styles.settingItemLeft}>
                <Icon name="notifications" size={24} color={theme.colors.primary} />
                <View style={styles.settingItemText}>
                  <Text style={[styles.settingItemTitle, { color: theme.colors.text }]}>
                    Notifications
                  </Text>
                  <Text style={[styles.settingItemSubtitle, { color: theme.colors.textSecondary }]}>
                    Enable daily usage reminders
                  </Text>
                </View>
              </View>
              <Switch
                value={settings.notificationsEnabled}
                onValueChange={(value) => updateSettings({ notificationsEnabled: value })}
                trackColor={{ false: theme.colors.textSecondary, true: theme.colors.primary }}
                thumbColor="#FFFFFF"
              />
            </View>

            {settings.notificationsEnabled && renderSettingItem(
              'Reminder Time',
              settings.reminderTime,
              () => {
                Alert.prompt(
                  'Reminder Time',
                  'Enter time in HH:MM format (24-hour):',
                  (text) => {
                    if (text && /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(text)) {
                      updateSettings({ reminderTime: text });
                    } else {
                      Alert.alert('Error', 'Please enter a valid time in HH:MM format.');
                    }
                  },
                  'plain-text',
                  settings.reminderTime
                );
              },
              'schedule',
              'Set daily reminder time'
            )}
          </View>
        )}

        {/* Appearance Settings */}
        {activeSection === 'appearance' && (
          <View style={styles.sectionContainer}>
            {renderSettingItem(
              'Theme',
              settings.theme,
              () => setShowThemeModal(true),
              'palette',
              'Choose your app theme'
            )}

            {renderSettingItem(
              'Font Family',
              settings.fontFamily,
              () => {
                Alert.alert(
                  'Font Family',
                  'Select a font family:',
                  FONT_FAMILIES.map(font => ({
                    text: font,
                    onPress: () => updateSettings({ fontFamily: font }),
                  }))
                );
              },
              'text-fields',
              'Select your preferred font'
            )}
          </View>
        )}

        {/* Reset Options */}
        {activeSection === 'reset' && (
          <View style={styles.sectionContainer}>
            <TouchableOpacity
              style={[styles.resetButton, { backgroundColor: theme.colors.warning }]}
              onPress={handleDashboardReset}>
              <Icon name="refresh" size={24} color="#FFFFFF" />
              <View style={styles.resetButtonText}>
                <Text style={styles.resetButtonTitle}>Dashboard Data Reset</Text>
                <Text style={styles.resetButtonSubtitle}>
                  Clear dashboard data only, keep history intact
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.resetButton, { backgroundColor: theme.colors.error }]}
              onPress={handleFactoryReset}>
              <Icon name="delete-forever" size={24} color="#FFFFFF" />
              <View style={styles.resetButtonText}>
                <Text style={styles.resetButtonTitle}>Factory Reset</Text>
                <Text style={styles.resetButtonSubtitle}>
                  Clear all data and settings - cannot be undone
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Currency Modal */}
      <Modal visible={showCurrencyModal} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Currency</Text>
            <ScrollView style={styles.modalList}>
              {DEFAULT_CURRENCIES.map((currency) => (
                <TouchableOpacity
                  key={currency.value}
                  style={styles.modalItem}
                  onPress={() => {
                    if (currency.value === 'CUSTOM') {
                      Alert.prompt(
                        'Custom Currency',
                        'Enter your custom currency name:',
                        (text) => {
                          if (text) {
                            updateSettings({ currency: 'CUSTOM', customCurrency: text });
                            setCustomCurrency(text);
                          }
                        }
                      );
                    } else {
                      updateSettings({ currency: currency.value });
                    }
                    setShowCurrencyModal(false);
                  }}>
                  <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                    {currency.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            <TouchableOpacity
              style={[styles.modalCloseButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => setShowCurrencyModal(false)}>
              <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Unit Type Modal */}
      <Modal visible={showUnitModal} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Unit Type</Text>
            <ScrollView style={styles.modalList}>
              {DEFAULT_UNIT_TYPES.map((unit) => (
                <TouchableOpacity
                  key={unit.value}
                  style={styles.modalItem}
                  onPress={() => {
                    if (unit.value === 'CUSTOM') {
                      Alert.prompt(
                        'Custom Unit Type',
                        'Enter your custom unit type:',
                        (text) => {
                          if (text) {
                            updateSettings({ unitType: 'CUSTOM', customUnitType: text });
                            setCustomUnit(text);
                          }
                        }
                      );
                    } else {
                      updateSettings({ unitType: unit.value });
                    }
                    setShowUnitModal(false);
                  }}>
                  <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                    {unit.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            <TouchableOpacity
              style={[styles.modalCloseButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => setShowUnitModal(false)}>
              <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Theme Modal */}
      <Modal visible={showThemeModal} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Theme</Text>
            <ScrollView style={styles.modalList}>
              {THEMES.map((themeOption) => (
                <TouchableOpacity
                  key={themeOption.name}
                  style={styles.modalItem}
                  onPress={() => {
                    updateSettings({ theme: themeOption.name });
                    setShowThemeModal(false);
                  }}>
                  <LinearGradient
                    colors={themeOption.gradients.primary}
                    style={styles.themePreview}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  />
                  <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                    {themeOption.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            <TouchableOpacity
              style={[styles.modalCloseButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => setShowThemeModal(false)}>
              <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    marginTop: 16,
    fontWeight: '500',
  },
  headerCard: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  headerSubtitle: {
    color: '#FFFFFF',
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.9,
  },
  sectionButtonsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 8,
  },
  sectionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  sectionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  scrollContainer: {
    flex: 1,
  },
  sectionContainer: {
    paddingHorizontal: 16,
  },
  setupCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  setupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  setupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  setupDescription: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  setupInput: {
    borderWidth: 2,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    marginBottom: 16,
  },
  setupButton: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  setupButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  setupButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItemText: {
    marginLeft: 12,
    flex: 1,
  },
  settingItemTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  settingItemSubtitle: {
    fontSize: 12,
    marginTop: 2,
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingItemValue: {
    fontSize: 14,
    marginRight: 8,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  resetButtonText: {
    marginLeft: 12,
    flex: 1,
  },
  resetButtonTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resetButtonSubtitle: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 2,
    opacity: 0.9,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    maxHeight: '70%',
    borderRadius: 16,
    padding: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalList: {
    maxHeight: 300,
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  modalItemText: {
    fontSize: 16,
    marginLeft: 12,
  },
  themePreview: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  modalCloseButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  modalCloseButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default Settings;

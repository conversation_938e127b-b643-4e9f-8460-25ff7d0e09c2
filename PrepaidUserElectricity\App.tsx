/**
 * Prepaid User - Electricity App
 * A modern electricity recording and usage tracking app
 *
 * @format
 */

import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { StatusBar, Alert, View, Text } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import screens
import Dashboard from './src/screens/Dashboard';
import Purchases from './src/screens/Purchases';
import Usage from './src/screens/Usage';
import History from './src/screens/History';
import Settings from './src/screens/Settings';

// Import utilities
import { isInitialSetupComplete } from './src/utils/storage';
import { initializeNotifications, requestNotificationPermissions } from './src/utils/notifications';
import { ThemeProvider, CustomDrawerContent } from './src/components';

const Drawer = createDrawerNavigator();

function App(): React.JSX.Element {
  const [initialSetupComplete, setInitialSetupComplete] = useState<boolean | null>(null);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    // Initialize notifications
    initializeNotifications();
    await requestNotificationPermissions();

    // Check initial setup
    checkInitialSetup();
  };

  const checkInitialSetup = async () => {
    try {
      const setupComplete = await isInitialSetupComplete();
      setInitialSetupComplete(setupComplete);

      if (!setupComplete) {
        Alert.alert(
          'Welcome to Prepaid User - Electricity',
          'Please complete the initial setup by entering your current unit value and configuring your preferences in Settings.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error checking initial setup:', error);
      setInitialSetupComplete(false);
    }
  };

  if (initialSetupComplete === null) {
    // Loading state - you can replace this with a proper loading component
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <ThemeProvider>
      <NavigationContainer>
        <StatusBar barStyle="light-content" backgroundColor="#007AFF" />
        <Drawer.Navigator
          initialRouteName="Dashboard"
          drawerContent={(props) => <CustomDrawerContent {...props} />}
          screenOptions={{
            headerStyle: {
              backgroundColor: '#007AFF',
            },
            headerTintColor: '#FFFFFF',
            headerTitleStyle: {
              fontWeight: 'bold',
            },
            drawerStyle: {
              backgroundColor: '#F2F2F7',
            },
            drawerActiveTintColor: '#007AFF',
            drawerInactiveTintColor: '#8E8E93',
          }}>
          <Drawer.Screen
            name="Dashboard"
            component={Dashboard}
            options={{
              title: 'Dashboard',
              drawerIcon: ({ color, size }) => (
                <Icon name="dashboard" size={size} color={color} />
              ),
            }}
          />
          <Drawer.Screen
            name="Purchases"
            component={Purchases}
            options={{
              title: 'Purchases',
              drawerIcon: ({ color, size }) => (
                <Icon name="shopping-cart" size={size} color={color} />
              ),
            }}
          />
          <Drawer.Screen
            name="Usage"
            component={Usage}
            options={{
              title: 'Usage',
              drawerIcon: ({ color, size }) => (
                <Icon name="trending-up" size={size} color={color} />
              ),
            }}
          />
          <Drawer.Screen
            name="History"
            component={History}
            options={{
              title: 'History',
              drawerIcon: ({ color, size }) => (
                <Icon name="history" size={size} color={color} />
              ),
            }}
          />
          <Drawer.Screen
            name="Settings"
            component={Settings}
            options={{
              title: 'Settings',
              drawerIcon: ({ color, size }) => (
                <Icon name="settings" size={size} color={color} />
              ),
            }}
          />
        </Drawer.Navigator>
      </NavigationContainer>
    </ThemeProvider>
  );
}

export default App;

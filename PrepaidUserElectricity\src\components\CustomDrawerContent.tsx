import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { DrawerContentScrollView, DrawerItemList } from '@react-navigation/drawer';
import LinearGradient from 'react-native-linear-gradient';
import { useTheme } from './ThemeProvider';
import AppLogo from './AppLogo';

const CustomDrawerContent = (props: any) => {
  const { theme } = useTheme();

  return (
    <DrawerContentScrollView {...props} contentContainerStyle={styles.container}>
      {/* Header with logo */}
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}>
        <AppLogo size={48} />
        <Text style={styles.appName}>PREPAID USER</Text>
        <Text style={styles.appSubtitle}>ELECTRICITY</Text>
      </LinearGradient>

      {/* Navigation items */}
      <View style={[styles.drawerItems, { backgroundColor: theme.colors.background }]}>
        <DrawerItemList {...props} />
      </View>

      {/* Footer */}
      <View style={[styles.footer, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          Version 1.0.0
        </Text>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          Modern Electricity Tracking
        </Text>
      </View>
    </DrawerContentScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 150,
  },
  appName: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 12,
    textAlign: 'center',
  },
  appSubtitle: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.9,
  },
  drawerItems: {
    flex: 1,
    paddingTop: 8,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    marginVertical: 2,
  },
});

export default CustomDrawerContent;

import { ThemeConfig } from '../types';

// Default currencies
export const DEFAULT_CURRENCIES = [
  { label: 'USD - US Dollar', value: 'USD', symbol: '$' },
  { label: 'EUR - Euro', value: 'EUR', symbol: '€' },
  { label: 'GBP - British Pound', value: 'GBP', symbol: '£' },
  { label: 'ZAR - South African Rand', value: 'ZAR', symbol: 'R' },
  { label: 'JPY - Japanese Yen', value: 'JPY', symbol: '¥' },
  { label: 'Custom', value: 'CUSTOM', symbol: '' },
];

// Default unit types
export const DEFAULT_UNIT_TYPES = [
  { label: 'Units', value: 'Units' },
  { label: 'KWh', value: 'KWh' },
  { label: 'Custom', value: 'CUSTOM' },
];

// App themes
export const THEMES: ThemeConfig[] = [
  {
    name: 'Electric Blue',
    colors: {
      primary: '#007AFF',
      secondary: '#5AC8FA',
      background: '#F2F2F7',
      surface: '#FFFFFF',
      text: '#000000',
      textSecondary: '#8E8E93',
      accent: '#FF9500',
      warning: '#FF9500',
      error: '#FF3B30',
      success: '#34C759',
    },
    gradients: {
      primary: ['#007AFF', '#5AC8FA'],
      secondary: ['#5AC8FA', '#007AFF'],
      background: ['#F2F2F7', '#E5E5EA'],
    },
  },
  {
    name: 'Lightning Yellow',
    colors: {
      primary: '#FFD60A',
      secondary: '#FF9500',
      background: '#1C1C1E',
      surface: '#2C2C2E',
      text: '#FFFFFF',
      textSecondary: '#8E8E93',
      accent: '#007AFF',
      warning: '#FF9500',
      error: '#FF453A',
      success: '#30D158',
    },
    gradients: {
      primary: ['#FFD60A', '#FF9500'],
      secondary: ['#FF9500', '#FFD60A'],
      background: ['#1C1C1E', '#2C2C2E'],
    },
  },
  {
    name: 'Power Green',
    colors: {
      primary: '#34C759',
      secondary: '#30D158',
      background: '#F2F2F7',
      surface: '#FFFFFF',
      text: '#000000',
      textSecondary: '#8E8E93',
      accent: '#007AFF',
      warning: '#FF9500',
      error: '#FF3B30',
      success: '#34C759',
    },
    gradients: {
      primary: ['#34C759', '#30D158'],
      secondary: ['#30D158', '#34C759'],
      background: ['#F2F2F7', '#E5E5EA'],
    },
  },
  {
    name: 'Midnight Purple',
    colors: {
      primary: '#AF52DE',
      secondary: '#BF5AF2',
      background: '#000000',
      surface: '#1C1C1E',
      text: '#FFFFFF',
      textSecondary: '#8E8E93',
      accent: '#FFD60A',
      warning: '#FF9500',
      error: '#FF453A',
      success: '#30D158',
    },
    gradients: {
      primary: ['#AF52DE', '#BF5AF2'],
      secondary: ['#BF5AF2', '#AF52DE'],
      background: ['#000000', '#1C1C1E'],
    },
  },
  {
    name: 'Sunset Orange',
    colors: {
      primary: '#FF6B35',
      secondary: '#FF9500',
      background: '#FFF8F0',
      surface: '#FFFFFF',
      text: '#000000',
      textSecondary: '#8E8E93',
      accent: '#007AFF',
      warning: '#FF9500',
      error: '#FF3B30',
      success: '#34C759',
    },
    gradients: {
      primary: ['#FF6B35', '#FF9500'],
      secondary: ['#FF9500', '#FF6B35'],
      background: ['#FFF8F0', '#FFE5CC'],
    },
  },
];

// Font families
export const FONT_FAMILIES = [
  'System',
  'Roboto',
  'Open Sans',
  'Lato',
  'Montserrat',
];

// Storage keys
export const STORAGE_KEYS = {
  SETTINGS: '@prepaid_electricity_settings',
  ELECTRICITY_DATA: '@prepaid_electricity_data',
  PURCHASE_HISTORY: '@prepaid_purchase_history',
  USAGE_HISTORY: '@prepaid_usage_history',
  HISTORY: '@prepaid_history',
  INITIAL_SETUP: '@prepaid_initial_setup',
};

// Default settings
export const DEFAULT_SETTINGS: any = {
  costPerUnit: 1.5,
  currency: 'USD',
  customCurrency: '',
  unitType: 'Units',
  customUnitType: '',
  lowUnitsThreshold: 10,
  theme: 'Electric Blue',
  fontFamily: 'System',
  primaryColor: '#007AFF',
  secondaryColor: '#5AC8FA',
  notificationsEnabled: true,
  reminderTime: '18:00',
};

import { PurchaseData, UsageData, HistoryEntry } from '../types';

/**
 * Calculate unit usage since last recording
 * @param previousUnits - Previous unit reading
 * @param currentUnits - Current unit reading
 * @returns Unit usage difference
 */
export function calculateUsage(previousUnits: number, currentUnits: number): number {
  return Math.abs(previousUnits - currentUnits);
}

/**
 * Calculate cost based on units and cost per unit
 * @param units - Number of units
 * @param costPerUnit - Cost per unit
 * @returns Total cost
 */
export function calculateCost(units: number, costPerUnit: number): number {
  return units * costPerUnit;
}

/**
 * Calculate units received from currency amount
 * @param currencyAmount - Amount of currency spent
 * @param costPerUnit - Cost per unit
 * @returns Number of units received
 */
export function calculateUnitsFromCurrency(currencyAmount: number, costPerUnit: number): number {
  return currencyAmount / costPerUnit;
}

/**
 * Calculate weekly totals from history
 * @param history - Array of history entries
 * @returns Weekly totals object
 */
export function calculateWeeklyTotals(history: HistoryEntry[]) {
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  const weeklyEntries = history.filter(entry => 
    new Date(entry.timestamp) >= oneWeekAgo
  );

  let totalUsage = 0;
  let totalCost = 0;
  let totalPurchases = 0;

  weeklyEntries.forEach(entry => {
    if (entry.type === 'usage') {
      const usageData = entry.data as UsageData;
      totalUsage += usageData.unitsUsed;
      totalCost += usageData.cost;
    } else if (entry.type === 'purchase') {
      const purchaseData = entry.data as PurchaseData;
      totalPurchases += purchaseData.currencyAmount;
    }
  });

  return {
    usage: totalUsage,
    cost: totalCost,
    purchases: totalPurchases,
  };
}

/**
 * Calculate monthly totals from history
 * @param history - Array of history entries
 * @returns Monthly totals object
 */
export function calculateMonthlyTotals(history: HistoryEntry[]) {
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

  const monthlyEntries = history.filter(entry => 
    new Date(entry.timestamp) >= oneMonthAgo
  );

  let totalUsage = 0;
  let totalCost = 0;
  let totalPurchases = 0;

  monthlyEntries.forEach(entry => {
    if (entry.type === 'usage') {
      const usageData = entry.data as UsageData;
      totalUsage += usageData.unitsUsed;
      totalCost += usageData.cost;
    } else if (entry.type === 'purchase') {
      const purchaseData = entry.data as PurchaseData;
      totalPurchases += purchaseData.currencyAmount;
    }
  });

  return {
    usage: totalUsage,
    cost: totalCost,
    purchases: totalPurchases,
  };
}

/**
 * Format currency value with symbol
 * @param amount - Currency amount
 * @param currency - Currency code
 * @param symbol - Currency symbol
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string, symbol: string = ''): string {
  const formattedAmount = amount.toFixed(2);
  if (symbol) {
    return `${symbol}${formattedAmount}`;
  }
  return `${formattedAmount} ${currency}`;
}

/**
 * Format units with unit type
 * @param units - Number of units
 * @param unitType - Type of units
 * @returns Formatted units string
 */
export function formatUnits(units: number, unitType: string): string {
  return `${units.toFixed(2)} ${unitType}`;
}

/**
 * Generate chart data for usage visualization
 * @param history - Array of history entries
 * @param days - Number of days to include
 * @returns Chart data object
 */
export function generateChartData(history: HistoryEntry[], days: number = 7) {
  const labels: string[] = [];
  const usageData: number[] = [];
  const costData: number[] = [];

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    const dayStart = new Date(date);
    dayStart.setHours(0, 0, 0, 0);
    
    const dayEnd = new Date(date);
    dayEnd.setHours(23, 59, 59, 999);

    const dayEntries = history.filter(entry => {
      const entryDate = new Date(entry.timestamp);
      return entryDate >= dayStart && entryDate <= dayEnd && entry.type === 'usage';
    });

    let dayUsage = 0;
    let dayCost = 0;

    dayEntries.forEach(entry => {
      const usageEntry = entry.data as UsageData;
      dayUsage += usageEntry.unitsUsed;
      dayCost += usageEntry.cost;
    });

    labels.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
    usageData.push(dayUsage);
    costData.push(dayCost);
  }

  return {
    labels,
    datasets: [
      {
        data: usageData,
        color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
        strokeWidth: 2,
      },
    ],
    costData,
  };
}

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ThemeConfig } from '../types';
import { THEMES } from '../utils/constants';
import { loadSettings } from '../utils/storage';

interface ThemeContextType {
  theme: ThemeConfig;
  setTheme: (themeName: string) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<ThemeConfig>(THEMES[0]);

  useEffect(() => {
    loadTheme();
  }, []);

  const loadTheme = async () => {
    try {
      const settings = await loadSettings();
      const selectedTheme = THEMES.find(t => t.name === settings.theme) || THEMES[0];
      setThemeState(selectedTheme);
    } catch (error) {
      console.error('Error loading theme:', error);
    }
  };

  const setTheme = (themeName: string) => {
    const selectedTheme = THEMES.find(t => t.name === themeName) || THEMES[0];
    setThemeState(selectedTheme);
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeProvider;

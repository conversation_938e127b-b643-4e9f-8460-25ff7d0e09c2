import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

import { useTheme } from '../components/ThemeProvider';
import { 
  loadSettings, 
  savePurchase, 
  loadElectricityData, 
  saveElectricityData 
} from '../utils/storage';
import { 
  calculateUnitsFromCurrency, 
  formatCurrency, 
  formatUnits 
} from '../utils/calculations';
import { AppSettings, PurchaseData, ElectricityData } from '../types';

const Purchases: React.FC = () => {
  const { theme } = useTheme();
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [currencyAmount, setCurrencyAmount] = useState('');
  const [unitsReceived, setUnitsReceived] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAppSettings();
  }, []);

  useEffect(() => {
    if (currencyAmount && settings) {
      const amount = parseFloat(currencyAmount) || 0;
      const units = calculateUnitsFromCurrency(amount, settings.costPerUnit);
      setUnitsReceived(units);
    } else {
      setUnitsReceived(0);
    }
  }, [currencyAmount, settings]);

  const loadAppSettings = async () => {
    try {
      const appSettings = await loadSettings();
      setSettings(appSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert('Error', 'Failed to load app settings.');
    }
  };

  const handlePurchase = async () => {
    if (!currencyAmount || !settings) {
      Alert.alert('Error', 'Please enter a valid currency amount.');
      return;
    }

    const amount = parseFloat(currencyAmount);
    if (amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount greater than 0.');
      return;
    }

    setIsLoading(true);

    try {
      // Create purchase record
      const purchase: PurchaseData = {
        id: Date.now().toString(),
        currencyAmount: amount,
        unitsReceived: unitsReceived,
        costPerUnit: settings.costPerUnit,
        timestamp: new Date(),
        currency: settings.currency === 'CUSTOM' ? settings.customCurrency || 'CUSTOM' : settings.currency,
      };

      // Save purchase to history
      await savePurchase(purchase);

      // Update current electricity data
      const currentData = await loadElectricityData();
      if (currentData) {
        const updatedData: ElectricityData = {
          ...currentData,
          currentUnits: currentData.currentUnits + unitsReceived,
          timestamp: new Date(),
        };
        await saveElectricityData(updatedData);
      }

      Alert.alert(
        'Purchase Recorded',
        `Successfully added ${formatUnits(unitsReceived, settings.unitType === 'CUSTOM' ? settings.customUnitType || 'Units' : settings.unitType)} to your account.`,
        [
          {
            text: 'OK',
            onPress: () => {
              setCurrencyAmount('');
              setUnitsReceived(0);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error recording purchase:', error);
      Alert.alert('Error', 'Failed to record purchase. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!settings) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Icon name="shopping-cart" size={48} color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading...</Text>
        </View>
      </View>
    );
  }

  const currencySymbol = settings.currency === 'CUSTOM' ? settings.customCurrency : 
    settings.currency === 'USD' ? '$' : 
    settings.currency === 'EUR' ? '€' : 
    settings.currency === 'GBP' ? '£' : 
    settings.currency === 'ZAR' ? 'R' : 
    settings.currency === 'JPY' ? '¥' : settings.currency;

  const unitType = settings.unitType === 'CUSTOM' ? settings.customUnitType : settings.unitType;

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        
        {/* Header */}
        <LinearGradient
          colors={theme.gradients.primary}
          style={styles.headerCard}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}>
          <Icon name="add-shopping-cart" size={32} color="#FFFFFF" />
          <Text style={styles.headerTitle}>Add Purchase</Text>
          <Text style={styles.headerSubtitle}>
            Enter the amount you spent to calculate units received
          </Text>
        </LinearGradient>

        {/* Currency Input */}
        <View style={[styles.inputCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.inputHeader}>
            <Icon name="attach-money" size={24} color={theme.colors.primary} />
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Amount Spent ({currencySymbol})
            </Text>
          </View>
          <TextInput
            style={[
              styles.currencyInput,
              { 
                color: theme.colors.text,
                borderColor: theme.colors.primary,
                backgroundColor: theme.colors.background,
              }
            ]}
            value={currencyAmount}
            onChangeText={setCurrencyAmount}
            placeholder={`Enter amount in ${currencySymbol}`}
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
            returnKeyType="done"
          />
        </View>

        {/* Live Preview */}
        <View style={[styles.previewCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.previewHeader}>
            <Icon name="preview" size={24} color={theme.colors.secondary} />
            <Text style={[styles.previewTitle, { color: theme.colors.text }]}>Live Preview</Text>
          </View>
          
          <View style={styles.previewContent}>
            <View style={styles.previewRow}>
              <Text style={[styles.previewLabel, { color: theme.colors.textSecondary }]}>
                Cost per {unitType}:
              </Text>
              <Text style={[styles.previewValue, { color: theme.colors.text }]}>
                {formatCurrency(settings.costPerUnit, settings.currency, currencySymbol)}
              </Text>
            </View>
            
            <View style={styles.previewRow}>
              <Text style={[styles.previewLabel, { color: theme.colors.textSecondary }]}>
                Units you'll receive:
              </Text>
              <Text style={[styles.previewValueLarge, { color: theme.colors.primary }]}>
                {formatUnits(unitsReceived, unitType)}
              </Text>
            </View>
            
            {currencyAmount && (
              <View style={styles.previewRow}>
                <Text style={[styles.previewLabel, { color: theme.colors.textSecondary }]}>
                  Total cost:
                </Text>
                <Text style={[styles.previewValue, { color: theme.colors.text }]}>
                  {formatCurrency(parseFloat(currencyAmount) || 0, settings.currency, currencySymbol)}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Purchase Button */}
        <TouchableOpacity
          style={[
            styles.purchaseButton,
            { 
              opacity: currencyAmount && !isLoading ? 1 : 0.5,
            }
          ]}
          onPress={handlePurchase}
          disabled={!currencyAmount || isLoading}>
          <LinearGradient
            colors={theme.gradients.primary}
            style={styles.purchaseButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}>
            <Icon 
              name={isLoading ? "hourglass-empty" : "add-shopping-cart"} 
              size={24} 
              color="#FFFFFF" 
            />
            <Text style={styles.purchaseButtonText}>
              {isLoading ? 'Recording Purchase...' : 'Record Purchase'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        {/* Info Card */}
        <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
          <Icon name="info" size={20} color={theme.colors.secondary} />
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            Your purchase will be automatically added to your current units balance and recorded in your history.
          </Text>
        </View>

      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    marginTop: 16,
    fontWeight: '500',
  },
  headerCard: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  headerSubtitle: {
    color: '#FFFFFF',
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.9,
  },
  inputCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  inputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  currencyInput: {
    borderWidth: 2,
    borderRadius: 8,
    padding: 16,
    fontSize: 18,
    fontWeight: '500',
  },
  previewCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  previewContent: {
    gap: 12,
  },
  previewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  previewLabel: {
    fontSize: 14,
    flex: 1,
  },
  previewValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  previewValueLarge: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  purchaseButton: {
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  purchaseButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
  },
  purchaseButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  infoCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
});

export default Purchases;

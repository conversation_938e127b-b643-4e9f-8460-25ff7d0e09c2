import { Dimensions, PixelRatio } from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Based on iPhone 6/7/8 dimensions
const guidelineBaseWidth = 375;
const guidelineBaseHeight = 667;

/**
 * Scale size based on screen width
 */
export const scale = (size: number): number => (SCREEN_WIDTH / guidelineBaseWidth) * size;

/**
 * Scale size based on screen height
 */
export const verticalScale = (size: number): number => (SCREEN_HEIGHT / guidelineBaseHeight) * size;

/**
 * Moderate scale - less aggressive scaling
 */
export const moderateScale = (size: number, factor: number = 0.5): number =>
  size + (scale(size) - size) * factor;

/**
 * Check if device is tablet
 */
export const isTablet = (): boolean => {
  const pixelDensity = PixelRatio.get();
  const adjustedWidth = SCREEN_WIDTH * pixelDensity;
  const adjustedHeight = SCREEN_HEIGHT * pixelDensity;
  
  if (pixelDensity < 2 && (adjustedWidth >= 1000 || adjustedHeight >= 1000)) {
    return true;
  } else {
    return (
      pixelDensity === 2 && (adjustedWidth >= 1920 || adjustedHeight >= 1920)
    );
  }
};

/**
 * Get responsive dimensions
 */
export const getResponsiveDimensions = () => ({
  width: SCREEN_WIDTH,
  height: SCREEN_HEIGHT,
  isTablet: isTablet(),
  isLandscape: SCREEN_WIDTH > SCREEN_HEIGHT,
});

/**
 * Get responsive font size
 */
export const getResponsiveFontSize = (size: number): number => {
  if (isTablet()) {
    return moderateScale(size, 0.3);
  }
  return moderateScale(size, 0.2);
};

/**
 * Get responsive padding/margin
 */
export const getResponsiveSpacing = (size: number): number => {
  if (isTablet()) {
    return scale(size * 1.2);
  }
  return scale(size);
};

/**
 * Get responsive icon size
 */
export const getResponsiveIconSize = (size: number): number => {
  if (isTablet()) {
    return moderateScale(size, 0.4);
  }
  return moderateScale(size, 0.2);
};

/**
 * Get grid columns based on screen size
 */
export const getGridColumns = (): number => {
  if (isTablet()) {
    return SCREEN_WIDTH > SCREEN_HEIGHT ? 4 : 3; // Landscape vs Portrait
  }
  return 2;
};

/**
 * Get card width for responsive grid
 */
export const getCardWidth = (columns: number = 2, margin: number = 16): number => {
  const totalMargin = margin * (columns + 1);
  return (SCREEN_WIDTH - totalMargin) / columns;
};

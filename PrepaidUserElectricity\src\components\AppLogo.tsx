import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path, LinearGradient, Defs, Stop } from 'react-native-svg';
import { useTheme } from './ThemeProvider';

interface AppLogoProps {
  size?: number;
  showGradient?: boolean;
}

const AppLogo: React.FC<AppLogoProps> = ({ size = 64, showGradient = true }) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size} viewBox="0 0 100 100">
        <Defs>
          <LinearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={theme.colors.primary} />
            <Stop offset="100%" stopColor={theme.colors.secondary} />
          </LinearGradient>
        </Defs>
        
        {/* Lightning bolt */}
        <Path
          d="M30 10 L70 10 L50 45 L65 45 L35 90 L50 55 L35 55 Z"
          fill={showGradient ? "url(#lightningGradient)" : theme.colors.primary}
          stroke="#FFFFFF"
          strokeWidth="2"
        />
        
        {/* Electric sparks */}
        <Path
          d="M20 25 L25 20 M25 30 L20 35 M75 20 L80 25 M80 30 L75 35"
          stroke={showGradient ? theme.colors.secondary : theme.colors.primary}
          strokeWidth="2"
          strokeLinecap="round"
        />
        
        {/* Power lines */}
        <Path
          d="M15 15 L85 15 M15 85 L85 85"
          stroke={theme.colors.accent}
          strokeWidth="1.5"
          strokeDasharray="5,5"
          opacity="0.6"
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AppLogo;

import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Svg, { Circle, Path, LinearGradient, Defs, Stop } from 'react-native-svg';
import { useTheme } from './ThemeProvider';

interface ModernDialProps {
  value: number;
  maxValue: number;
  title: string;
  subtitle?: string;
  size?: number;
  unit?: string;
}

const { width } = Dimensions.get('window');

const ModernDial: React.FC<ModernDialProps> = ({
  value,
  maxValue,
  title,
  subtitle,
  size = width * 0.6,
  unit = '',
}) => {
  const { theme } = useTheme();
  
  const radius = (size - 40) / 2;
  const strokeWidth = 12;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const progress = Math.min(value / maxValue, 1);
  const strokeDashoffset = circumference - progress * circumference;

  // Calculate angle for the indicator
  const angle = (progress * 270) - 135; // 270 degrees total, starting from -135 degrees
  const indicatorX = radius + (normalizedRadius - 10) * Math.cos((angle * Math.PI) / 180);
  const indicatorY = radius + (normalizedRadius - 10) * Math.sin((angle * Math.PI) / 180);

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size}>
        <Defs>
          <LinearGradient id="dialGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={theme.colors.primary} />
            <Stop offset="50%" stopColor={theme.colors.secondary} />
            <Stop offset="100%" stopColor={theme.colors.accent} />
          </LinearGradient>
          <LinearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={theme.colors.textSecondary} stopOpacity="0.2" />
            <Stop offset="100%" stopColor={theme.colors.textSecondary} stopOpacity="0.1" />
          </LinearGradient>
        </Defs>
        
        {/* Background circle */}
        <Circle
          stroke="url(#backgroundGradient)"
          fill="none"
          cx={radius}
          cy={radius}
          r={normalizedRadius}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={circumference * 0.125} // Start from -135 degrees
        />
        
        {/* Progress circle */}
        <Circle
          stroke="url(#dialGradient)"
          fill="none"
          cx={radius}
          cy={radius}
          r={normalizedRadius}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset + (circumference * 0.125)}
          transform={`rotate(-135 ${radius} ${radius})`}
        />
        
        {/* Center indicator dot */}
        <Circle
          cx={indicatorX}
          cy={indicatorY}
          r="6"
          fill={theme.colors.primary}
          stroke="#FFFFFF"
          strokeWidth="2"
        />
        
        {/* Center circle */}
        <Circle
          cx={radius}
          cy={radius}
          r="8"
          fill={theme.colors.surface}
          stroke={theme.colors.primary}
          strokeWidth="2"
        />
      </Svg>
      
      {/* Center content */}
      <View style={styles.centerContent}>
        <Text style={[styles.title, { color: theme.colors.text }]}>{title}</Text>
        <Text style={[styles.value, { color: theme.colors.primary }]}>
          {value.toFixed(1)}{unit}
        </Text>
        {subtitle && (
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            {subtitle}
          </Text>
        )}
        <Text style={[styles.percentage, { color: theme.colors.secondary }]}>
          {(progress * 100).toFixed(0)}%
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  centerContent: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 4,
  },
  value: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 2,
  },
  percentage: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 4,
  },
});

export default ModernDial;

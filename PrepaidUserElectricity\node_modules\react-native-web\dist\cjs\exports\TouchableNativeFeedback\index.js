"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _UnimplementedView = _interopRequireDefault(require("../../modules/UnimplementedView"));
/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */
var _default = exports.default = _UnimplementedView.default;
module.exports = exports.default;
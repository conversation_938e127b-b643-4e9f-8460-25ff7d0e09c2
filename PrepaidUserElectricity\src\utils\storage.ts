import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppSettings, ElectricityData, PurchaseData, UsageData, HistoryEntry } from '../types';
import { STORAGE_KEYS, DEFAULT_SETTINGS } from './constants';

/**
 * Save app settings to storage
 */
export async function saveSettings(settings: AppSettings): Promise<void> {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving settings:', error);
    throw error;
  }
}

/**
 * Load app settings from storage
 */
export async function loadSettings(): Promise<AppSettings> {
  try {
    const settingsJson = await AsyncStorage.getItem(STORAGE_KEYS.SETTINGS);
    if (settingsJson) {
      return JSON.parse(settingsJson);
    }
    return DEFAULT_SETTINGS;
  } catch (error) {
    console.error('Error loading settings:', error);
    return DEFAULT_SETTINGS;
  }
}

/**
 * Save electricity data to storage
 */
export async function saveElectricityData(data: ElectricityData): Promise<void> {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.ELECTRICITY_DATA, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving electricity data:', error);
    throw error;
  }
}

/**
 * Load electricity data from storage
 */
export async function loadElectricityData(): Promise<ElectricityData | null> {
  try {
    const dataJson = await AsyncStorage.getItem(STORAGE_KEYS.ELECTRICITY_DATA);
    if (dataJson) {
      return JSON.parse(dataJson);
    }
    return null;
  } catch (error) {
    console.error('Error loading electricity data:', error);
    return null;
  }
}

/**
 * Save purchase to history
 */
export async function savePurchase(purchase: PurchaseData): Promise<void> {
  try {
    const historyJson = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY);
    let history: HistoryEntry[] = historyJson ? JSON.parse(historyJson) : [];
    
    const historyEntry: HistoryEntry = {
      id: purchase.id,
      type: 'purchase',
      data: purchase,
      timestamp: purchase.timestamp,
    };
    
    history.unshift(historyEntry);
    await AsyncStorage.setItem(STORAGE_KEYS.HISTORY, JSON.stringify(history));
  } catch (error) {
    console.error('Error saving purchase:', error);
    throw error;
  }
}

/**
 * Save usage to history
 */
export async function saveUsage(usage: UsageData): Promise<void> {
  try {
    const historyJson = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY);
    let history: HistoryEntry[] = historyJson ? JSON.parse(historyJson) : [];
    
    const historyEntry: HistoryEntry = {
      id: usage.id,
      type: 'usage',
      data: usage,
      timestamp: usage.timestamp,
    };
    
    history.unshift(historyEntry);
    await AsyncStorage.setItem(STORAGE_KEYS.HISTORY, JSON.stringify(history));
  } catch (error) {
    console.error('Error saving usage:', error);
    throw error;
  }
}

/**
 * Load history from storage
 */
export async function loadHistory(): Promise<HistoryEntry[]> {
  try {
    const historyJson = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY);
    if (historyJson) {
      return JSON.parse(historyJson);
    }
    return [];
  } catch (error) {
    console.error('Error loading history:', error);
    return [];
  }
}

/**
 * Check if initial setup is complete
 */
export async function isInitialSetupComplete(): Promise<boolean> {
  try {
    const setupComplete = await AsyncStorage.getItem(STORAGE_KEYS.INITIAL_SETUP);
    return setupComplete === 'true';
  } catch (error) {
    console.error('Error checking initial setup:', error);
    return false;
  }
}

/**
 * Mark initial setup as complete
 */
export async function markInitialSetupComplete(): Promise<void> {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.INITIAL_SETUP, 'true');
  } catch (error) {
    console.error('Error marking initial setup complete:', error);
    throw error;
  }
}

/**
 * Factory reset - clear all data
 */
export async function factoryReset(): Promise<void> {
  try {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.SETTINGS,
      STORAGE_KEYS.ELECTRICITY_DATA,
      STORAGE_KEYS.HISTORY,
      STORAGE_KEYS.INITIAL_SETUP,
    ]);
  } catch (error) {
    console.error('Error performing factory reset:', error);
    throw error;
  }
}

/**
 * Dashboard data reset - clear only dashboard data, keep history
 */
export async function dashboardDataReset(): Promise<void> {
  try {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.ELECTRICITY_DATA,
    ]);
  } catch (error) {
    console.error('Error performing dashboard data reset:', error);
    throw error;
  }
}

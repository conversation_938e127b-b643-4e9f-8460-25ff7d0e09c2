// Type definitions for the app

export interface ElectricityData {
  id: string;
  currentUnits: number;
  previousUnits: number;
  usageSinceLastRecording: number;
  timestamp: Date;
}

export interface PurchaseData {
  id: string;
  currencyAmount: number;
  unitsReceived: number;
  costPerUnit: number;
  timestamp: Date;
  currency: string;
}

export interface UsageData {
  id: string;
  previousReading: number;
  currentReading: number;
  unitsUsed: number;
  timestamp: Date;
  cost: number;
}

export interface HistoryEntry {
  id: string;
  type: 'purchase' | 'usage';
  data: PurchaseData | UsageData;
  timestamp: Date;
}

export interface AppSettings {
  costPerUnit: number;
  currency: string;
  customCurrency?: string;
  unitType: string;
  customUnitType?: string;
  lowUnitsThreshold: number;
  theme: string;
  fontFamily: string;
  primaryColor: string;
  secondaryColor: string;
  notificationsEnabled: boolean;
  reminderTime: string;
}

export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    accent: string;
    warning: string;
    error: string;
    success: string;
  };
  gradients: {
    primary: string[];
    secondary: string[];
    background: string[];
  };
}

export interface WeeklyMonthlyData {
  weekly: {
    usage: number;
    cost: number;
    purchases: number;
  };
  monthly: {
    usage: number;
    cost: number;
    purchases: number;
  };
}

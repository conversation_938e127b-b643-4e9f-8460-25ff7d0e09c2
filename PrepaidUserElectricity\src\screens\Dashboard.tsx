import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

import { useTheme } from '../components/ThemeProvider';
import { 
  loadElectricityData, 
  loadSettings, 
  loadHistory,
  isInitialSetupComplete 
} from '../utils/storage';
import { 
  calculateWeeklyTotals, 
  calculateMonthlyTotals, 
  formatCurrency, 
  formatUnits 
} from '../utils/calculations';
import { ElectricityData, AppSettings, HistoryEntry } from '../types';

const { width } = Dimensions.get('window');

interface DashboardProps {
  navigation: any;
}

const Dashboard: React.FC<DashboardProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const [electricityData, setElectricityData] = useState<ElectricityData | null>(null);
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [weeklyData, setWeeklyData] = useState({ usage: 0, cost: 0, purchases: 0 });
  const [monthlyData, setMonthlyData] = useState({ usage: 0, cost: 0, purchases: 0 });
  const [showLowUnitsWarning, setShowLowUnitsWarning] = useState(false);

  useFocusEffect(
    useCallback(() => {
      loadDashboardData();
    }, [])
  );

  const loadDashboardData = async () => {
    try {
      const setupComplete = await isInitialSetupComplete();
      if (!setupComplete) {
        Alert.alert(
          'Setup Required',
          'Please complete the initial setup in Settings before using the app.',
          [
            {
              text: 'Go to Settings',
              onPress: () => navigation.navigate('Settings'),
            },
          ]
        );
        return;
      }

      const [data, appSettings, history] = await Promise.all([
        loadElectricityData(),
        loadSettings(),
        loadHistory(),
      ]);

      setElectricityData(data);
      setSettings(appSettings);

      if (history.length > 0) {
        const weekly = calculateWeeklyTotals(history);
        const monthly = calculateMonthlyTotals(history);
        setWeeklyData(weekly);
        setMonthlyData(monthly);
      }

      // Check for low units warning
      if (data && appSettings && data.currentUnits <= appSettings.lowUnitsThreshold) {
        setShowLowUnitsWarning(true);
      } else {
        setShowLowUnitsWarning(false);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data. Please try again.');
    }
  };

  const dismissLowUnitsWarning = () => {
    setShowLowUnitsWarning(false);
  };

  const renderQuickActionButton = (title: string, icon: string, onPress: () => void) => (
    <TouchableOpacity style={[styles.quickActionButton, { borderColor: theme.colors.primary }]} onPress={onPress}>
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.quickActionGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}>
        <Icon name={icon} size={20} color="#FFFFFF" />
        <Text style={styles.quickActionText}>{title}</Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderDataCard = (title: string, value: string, icon: string, subtitle?: string) => (
    <View style={[styles.dataCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.dataCardHeader}>
        <Icon name={icon} size={24} color={theme.colors.primary} />
        <Text style={[styles.dataCardTitle, { color: theme.colors.text }]}>{title}</Text>
      </View>
      <Text style={[styles.dataCardValue, { color: theme.colors.primary }]}>{value}</Text>
      {subtitle && (
        <Text style={[styles.dataCardSubtitle, { color: theme.colors.textSecondary }]}>{subtitle}</Text>
      )}
    </View>
  );

  if (!electricityData || !settings) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Icon name="flash-on" size={48} color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading Dashboard...</Text>
        </View>
      </View>
    );
  }

  const currencySymbol = settings.currency === 'CUSTOM' ? settings.customCurrency : 
    settings.currency === 'USD' ? '$' : 
    settings.currency === 'EUR' ? '€' : 
    settings.currency === 'GBP' ? '£' : 
    settings.currency === 'ZAR' ? 'R' : 
    settings.currency === 'JPY' ? '¥' : settings.currency;

  const unitType = settings.unitType === 'CUSTOM' ? settings.customUnitType : settings.unitType;

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Low Units Warning */}
      {showLowUnitsWarning && (
        <View style={[styles.warningCard, { backgroundColor: theme.colors.warning }]}>
          <View style={styles.warningContent}>
            <Icon name="warning" size={24} color="#FFFFFF" />
            <Text style={styles.warningText}>
              Low Units Warning! Only {electricityData.currentUnits} {unitType} remaining.
            </Text>
          </View>
          <TouchableOpacity onPress={dismissLowUnitsWarning}>
            <Icon name="close" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      )}

      {/* Current Units Display */}
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.currentUnitsCard}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}>
        <Icon name="flash-on" size={32} color="#FFFFFF" />
        <Text style={styles.currentUnitsLabel}>Current Units</Text>
        <Text style={styles.currentUnitsValue}>
          {formatUnits(electricityData.currentUnits, unitType)}
        </Text>
        <Text style={styles.usageSinceLastText}>
          Usage since last recording: {formatUnits(electricityData.usageSinceLastRecording, unitType)}
        </Text>
      </LinearGradient>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quick Actions</Text>
        {renderQuickActionButton('Add Purchase', 'add-shopping-cart', () => navigation.navigate('Purchases'))}
        {renderQuickActionButton('Record Usage', 'trending-up', () => navigation.navigate('Usage'))}
        {renderQuickActionButton('View History', 'history', () => navigation.navigate('History'))}
      </View>

      {/* Weekly and Monthly Data */}
      <View style={styles.dataContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Usage Summary</Text>
        
        <View style={styles.dataRow}>
          {renderDataCard(
            'Weekly Usage',
            formatUnits(weeklyData.usage, unitType),
            'date-range',
            formatCurrency(weeklyData.cost, settings.currency, currencySymbol)
          )}
          {renderDataCard(
            'Weekly Purchases',
            formatCurrency(weeklyData.purchases, settings.currency, currencySymbol),
            'shopping-cart'
          )}
        </View>

        <View style={styles.dataRow}>
          {renderDataCard(
            'Monthly Usage',
            formatUnits(monthlyData.usage, unitType),
            'calendar-today',
            formatCurrency(monthlyData.cost, settings.currency, currencySymbol)
          )}
          {renderDataCard(
            'Monthly Purchases',
            formatCurrency(monthlyData.purchases, settings.currency, currencySymbol),
            'account-balance-wallet'
          )}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    marginTop: 16,
    fontWeight: '500',
  },
  warningCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  warningContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  warningText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  currentUnitsCard: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  currentUnitsLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    marginTop: 8,
  },
  currentUnitsValue: {
    color: '#FFFFFF',
    fontSize: 32,
    fontWeight: 'bold',
    marginTop: 4,
  },
  usageSinceLastText: {
    color: '#FFFFFF',
    fontSize: 14,
    marginTop: 8,
    opacity: 0.9,
  },
  quickActionsContainer: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  quickActionButton: {
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  quickActionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  quickActionText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  dataContainer: {
    margin: 16,
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  dataCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dataCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  dataCardTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  dataCardValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  dataCardSubtitle: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default Dashboard;

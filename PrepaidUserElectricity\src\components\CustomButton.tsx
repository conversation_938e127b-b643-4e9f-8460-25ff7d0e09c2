import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from './ThemeProvider';

interface CustomButtonProps {
  title: string;
  onPress: () => void;
  icon?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  title,
  onPress,
  icon,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  style,
  textStyle,
}) => {
  const { theme } = useTheme();

  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[size]];
    
    if (variant === 'outline') {
      return [
        ...baseStyle,
        {
          borderWidth: 2,
          borderColor: theme.colors.primary,
          backgroundColor: 'transparent',
        },
        style,
      ];
    }
    
    return [...baseStyle, style];
  };

  const getTextColor = () => {
    if (variant === 'outline') {
      return theme.colors.primary;
    }
    return '#FFFFFF';
  };

  const getGradientColors = () => {
    if (variant === 'secondary') {
      return theme.gradients.secondary;
    }
    return theme.gradients.primary;
  };

  if (variant === 'outline') {
    return (
      <TouchableOpacity
        style={[getButtonStyle(), { opacity: disabled ? 0.5 : 1 }]}
        onPress={onPress}
        disabled={disabled}>
        <Icon name={icon || 'check'} size={20} color={getTextColor()} />
        <Text style={[styles.buttonText, { color: getTextColor() }, textStyle]}>
          {title}
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[{ opacity: disabled ? 0.5 : 1 }, style]}
      onPress={onPress}
      disabled={disabled}>
      <LinearGradient
        colors={getGradientColors()}
        style={[styles.button, styles[size]]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}>
        {icon && <Icon name={icon} size={20} color={getTextColor()} />}
        <Text style={[styles.buttonText, { color: getTextColor() }, textStyle]}>
          {title}
        </Text>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    gap: 8,
  },
  small: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  medium: {
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  large: {
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CustomButton;

"use strict";

exports.__esModule = true;
exports.default = void 0;
/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */

var getBoundingClientRect = node => {
  if (node != null) {
    var isElement = node.nodeType === 1; /* Node.ELEMENT_NODE */
    if (isElement && typeof node.getBoundingClientRect === 'function') {
      return node.getBoundingClientRect();
    }
  }
};
var _default = exports.default = getBoundingClientRect;
module.exports = exports.default;
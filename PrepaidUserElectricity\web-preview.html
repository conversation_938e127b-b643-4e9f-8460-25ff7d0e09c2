<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prepaid User - Electricity App Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .phone-container {
            width: 375px;
            height: 667px;
            background: #000;
            border-radius: 30px;
            padding: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #F2F2F7;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            padding: 50px 20px 20px;
            text-align: center;
            color: white;
        }
        
        .logo {
            width: 48px;
            height: 48px;
            margin: 0 auto 12px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .app-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .app-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .content {
            padding: 20px;
            height: calc(100% - 140px);
            overflow-y: auto;
        }
        
        .dial-container {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .dial {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            position: relative;
            background: conic-gradient(from 0deg, #007AFF, #5AC8FA, #FF9500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .dial-inner {
            width: 160px;
            height: 160px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .dial-title {
            font-size: 14px;
            color: #8E8E93;
            margin-bottom: 8px;
        }
        
        .dial-value {
            font-size: 28px;
            font-weight: bold;
            color: #007AFF;
            margin-bottom: 4px;
        }
        
        .dial-subtitle {
            font-size: 12px;
            color: #8E8E93;
        }
        
        .quick-actions {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #000;
            margin-bottom: 16px;
        }
        
        .action-button {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 12px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
        }
        
        .action-button .icon {
            margin-right: 8px;
            font-size: 20px;
        }
        
        .data-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .data-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .data-card-icon {
            font-size: 24px;
            color: #007AFF;
            margin-bottom: 8px;
        }
        
        .data-card-title {
            font-size: 14px;
            color: #8E8E93;
            margin-bottom: 4px;
        }
        
        .data-card-value {
            font-size: 18px;
            font-weight: bold;
            color: #000;
        }
        
        .data-card-subtitle {
            font-size: 12px;
            color: #8E8E93;
            margin-top: 2px;
        }
        
        .navigation-hint {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            color: #8E8E93;
            font-size: 12px;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 8px;
        }
        
        .preview-note {
            text-align: center;
            margin-top: 20px;
            color: white;
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 10px;
        }
        
        .preview-note h3 {
            margin-bottom: 10px;
        }
        
        .preview-note p {
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <div class="header">
                <div class="logo">⚡</div>
                <div class="app-title">PREPAID USER</div>
                <div class="app-subtitle">ELECTRICITY</div>
            </div>
            
            <div class="content">
                <div class="dial-container">
                    <div class="dial">
                        <div class="dial-inner">
                            <div class="dial-title">Current Units</div>
                            <div class="dial-value">85.5</div>
                            <div class="dial-subtitle">Units</div>
                        </div>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <div class="section-title">Quick Actions</div>
                    <button class="action-button">
                        <span class="icon">🛒</span>
                        Add Purchase
                    </button>
                    <button class="action-button">
                        <span class="icon">📈</span>
                        Record Usage
                    </button>
                    <button class="action-button">
                        <span class="icon">📋</span>
                        View History
                    </button>
                </div>
                
                <div class="data-cards">
                    <div class="data-card">
                        <div class="data-card-icon">📅</div>
                        <div class="data-card-title">Weekly Usage</div>
                        <div class="data-card-value">12.5</div>
                        <div class="data-card-subtitle">$18.75</div>
                    </div>
                    <div class="data-card">
                        <div class="data-card-icon">🛒</div>
                        <div class="data-card-title">Weekly Purchases</div>
                        <div class="data-card-value">$25.00</div>
                    </div>
                    <div class="data-card">
                        <div class="data-card-icon">📊</div>
                        <div class="data-card-title">Monthly Usage</div>
                        <div class="data-card-value">45.2</div>
                        <div class="data-card-subtitle">$67.80</div>
                    </div>
                    <div class="data-card">
                        <div class="data-card-icon">💰</div>
                        <div class="data-card-title">Monthly Purchases</div>
                        <div class="data-card-value">$85.00</div>
                    </div>
                </div>
            </div>
            
            <div class="navigation-hint">
                📱 Swipe from left edge to open navigation menu
            </div>
        </div>
    </div>
    
    <div class="preview-note">
        <h3>🎉 App Preview - Dashboard Screen</h3>
        <p>This is a static preview of your Prepaid User - Electricity app. The actual app includes:</p>
        <br>
        <p>✅ Interactive gradient dial with animations<br>
        ✅ 5 beautiful themes to choose from<br>
        ✅ Live calculations and real-time updates<br>
        ✅ Hamburger menu with 5 main sections<br>
        ✅ Charts, graphs, and data visualization<br>
        ✅ Complete settings and customization</p>
    </div>
</body>
</html>
